# Use Case 002: Business Profile Setup - Task Breakdown

**Parent Use Case:** 002-business-profile-setup  
**Title:** Set up business profile and company details  
**Target User:** Newly registered solo surveyor  

## Overview
Break down the business profile setup into discrete, manageable tasks that can be completed by a single agent run. Each task should include front-end components, API endpoints, validation, tests, and documentation as appropriate.

---

## Task 002-001: Create Business Profile Data Model
**Estimated Time:** 1-2 hours (including tests/docs)  
**Agent Time:** 2-3 minutes  

### Description
Define and implement the business profile data structure in Supabase and TypeScript interfaces.

### Deliverables
- [ ] Create `business_profiles` table in Supabase with RLS policies
- [ ] Define TypeScript interfaces for BusinessProfile
- [ ] Create database migration scripts
- [ ] Add table documentation
- [ ] Write unit tests for data model validation

### Dependencies
- 001-authentication-signup (user authentication must exist)
- Supabase project setup

### Technical Considerations
- Must include tenant_id for multi-tenancy
- RLS policies for data isolation
- Consider UK business requirements (Companies House integration future)

---

## Task 002-002: Create Business Profile Form Component
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Build the main business profile form component with basic company information fields.

### Deliverables
- [ ] Create BusinessProfileForm React component
- [ ] Implement company name input field with validation
- [ ] Implement trading name input field with validation
- [ ] Add form state management (React Hook Form or similar)
- [ ] Create component tests
- [ ] Add Storybook stories for component
- [ ] Document component API

### Dependencies
- 002-001 (data model must exist)
- UI component library setup

### Technical Considerations
- Use Ionic form components for mobile optimization
- Implement real-time validation
- Consider offline form state persistence

---

## Task 002-003: Implement Business Address Management
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Add business address and contact details functionality to the profile form.

### Deliverables
- [ ] Create AddressInput component with UK postcode validation
- [ ] Add phone number input with UK format validation
- [ ] Add email input with validation
- [ ] Implement address autocomplete (future: integrate with UK postcode API)
- [ ] Create address validation tests
- [ ] Document address handling patterns

### Dependencies
- 002-002 (form component must exist)

### Technical Considerations
- UK postcode validation patterns
- Consider future integration with Royal Mail API
- Mobile-friendly address input UX

---

## Task 002-004: Create Logo Upload Component
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Implement company logo upload functionality with validation and storage.

### Deliverables
- [ ] Create LogoUpload component with drag-and-drop
- [ ] Implement file size validation (max 2MB)
- [ ] Implement file format validation (PNG, JPG, SVG)
- [ ] Add image preview functionality
- [ ] Create Supabase storage bucket for logos
- [ ] Implement upload progress indicator
- [ ] Create upload component tests
- [ ] Document file upload patterns

### Dependencies
- 002-001 (data model must exist)
- Supabase storage configuration

### Technical Considerations
- Mobile bandwidth optimization
- Image compression before upload
- Fallback for devices without camera access

---

## Task 002-005: Implement Professional Qualifications Management
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Add professional qualifications selection and management to the business profile.

### Deliverables
- [ ] Create qualifications data structure (BOHS P402, RICS, etc.)
- [ ] Implement QualificationsSelector component
- [ ] Add custom qualification entry option
- [ ] Create qualification validation logic
- [ ] Implement qualification expiry date tracking
- [ ] Create qualifications tests
- [ ] Document UK qualification standards

### Dependencies
- 002-002 (form component must exist)

### Technical Considerations
- Pre-populate with UK industry standards
- Consider future integration with professional body APIs
- Qualification verification workflows (future)

---

## Task 002-006: Implement Survey Types Configuration
**Estimated Time:** 1-2 hours (including tests/docs)  
**Agent Time:** 2-3 minutes  

### Description
Add survey type selection to define the business's primary service offerings.

### Deliverables
- [ ] Create survey types data structure
- [ ] Implement SurveyTypesSelector component
- [ ] Add multi-select functionality for survey types
- [ ] Create survey type validation
- [ ] Implement custom survey type entry
- [ ] Create survey types tests
- [ ] Document survey type categories

### Dependencies
- 002-002 (form component must exist)

### Technical Considerations
- Align with competitive analysis findings
- Consider future service expansion
- Integration with inspection templates

---

## Task 002-007: Create Report Branding Configuration
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Implement default report branding and footer configuration.

### Deliverables
- [ ] Create ReportBrandingConfig component
- [ ] Implement footer text editor
- [ ] Add branding color picker
- [ ] Create branding preview component
- [ ] Implement branding template system
- [ ] Create branding tests
- [ ] Document branding customization options

### Dependencies
- 002-004 (logo upload must exist)
- 002-002 (form component must exist)

### Technical Considerations
- Preview functionality for report appearance
- Consider PDF generation requirements
- Mobile-friendly color picker

---

## Task 002-008: Implement Business Profile API Endpoints
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Create backend API endpoints for business profile CRUD operations.

### Deliverables
- [ ] Create POST /api/business-profile endpoint
- [ ] Create GET /api/business-profile endpoint
- [ ] Create PUT /api/business-profile endpoint
- [ ] Implement proper error handling and validation
- [ ] Add API authentication middleware
- [ ] Create API integration tests
- [ ] Document API endpoints (OpenAPI/Swagger)

### Dependencies
- 002-001 (data model must exist)
- API framework setup

### Technical Considerations
- Implement proper RLS policy enforcement
- File upload handling for logos
- Rate limiting for API endpoints

---

## Task 002-009: Implement Profile Progress Tracking
**Estimated Time:** 1-2 hours (including tests/docs)  
**Agent Time:** 2-3 minutes  

### Description
Add progress indicator to track business profile completion status.

### Deliverables
- [ ] Create ProfileProgress component
- [ ] Implement completion percentage calculation
- [ ] Add visual progress indicator
- [ ] Create progress validation logic
- [ ] Implement progress persistence
- [ ] Create progress tests
- [ ] Document progress tracking patterns

### Dependencies
- 002-002 (form component must exist)

### Technical Considerations
- Real-time progress updates
- Consider gamification elements
- Mobile-optimized progress display

---

## Task 002-010: Implement Offline Profile Management
**Estimated Time:** 2-3 hours (including tests/docs)  
**Agent Time:** 3-4 minutes  

### Description
Add offline capability for business profile creation and editing.

### Deliverables
- [ ] Implement local storage for profile data
- [ ] Create offline/online sync logic
- [ ] Add conflict resolution for sync
- [ ] Implement offline indicator UI
- [ ] Create sync queue management
- [ ] Create offline functionality tests
- [ ] Document offline patterns

### Dependencies
- 002-008 (API endpoints must exist)
- 002-002 (form component must exist)

### Technical Considerations
- Handle partial form completion offline
- Conflict resolution strategies
- Background sync when connection restored

---

## Task 002-011: Create Profile Edit Functionality
**Estimated Time:** 1-2 hours (including tests/docs)  
**Agent Time:** 2-3 minutes  

### Description
Implement the ability to edit business profile after initial setup.

### Deliverables
- [ ] Create ProfileEdit component
- [ ] Implement edit mode toggle
- [ ] Add change tracking and dirty state
- [ ] Create save/cancel functionality
- [ ] Implement edit validation
- [ ] Create edit functionality tests
- [ ] Document edit workflows

### Dependencies
- 002-002 (form component must exist)
- 002-008 (API endpoints must exist)

### Technical Considerations
- Preserve unsaved changes warning
- Optimistic updates with rollback
- Edit permissions and validation

---

## Task 002-012: Integration Testing and Documentation
**Estimated Time:** 2-3 hours  
**Agent Time:** 3-4 minutes  

### Description
Create comprehensive integration tests and user documentation for the complete business profile feature.

### Deliverables
- [ ] Create end-to-end tests for complete profile setup flow
- [ ] Create user documentation for business profile setup
- [ ] Create developer documentation for profile components
- [ ] Implement accessibility testing
- [ ] Create performance testing for file uploads
- [ ] Document troubleshooting guide

### Dependencies
- All previous 002-xxx tasks must be complete

### Technical Considerations
- Test offline/online scenarios
- Test file upload edge cases
- Accessibility compliance (WCAG 2.1)

---

## Summary
**Total Tasks:** 12  
**Estimated Development Time:** 20-30 hours  
**Estimated Agent Time:** 30-40 minutes total  

This breakdown ensures each task is focused, manageable, and includes all necessary components (frontend, backend, tests, documentation) while maintaining clear dependencies and technical considerations.
